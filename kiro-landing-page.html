<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="NovaX AI - 您的科研灵感引擎，构建前瞻性研究蓝图。提供个性化研究规划、战略性课题集群和前瞻性领域洞察。">
    <meta name="keywords" content="NovaX AI,科研灵感引擎,研究规划,学术研究,科研方向,创新思路,研究设计">
    <meta name="author" content="NovaX AI">
    <meta property="og:title" content="NovaX AI - 您的科研灵感引擎">
    <meta property="og:description" content="构建前瞻性研究蓝图，提供个性化研究规划和战略性课题集群。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://ai.medon.com.cn/novax/">
    <meta property="og:image" content="https://static.medsci.cn/ai-write/NovaX-0612.webp">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="NovaX AI - 您的科研灵感引擎">
    <meta name="twitter:description" content="构建前瞻性研究蓝图，提供个性化研究规划和战略性课题集群。">
    <title>NovaX AI - 您的科研灵感引擎</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.5;
            color: #e5e5e5;
            background-color: #0a0a0a;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header */
        header {
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            height: 72px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            text-decoration: none;
        }

        .logo-text {
            margin-right: 8px;
        }

        .preview-badge {
            background: #ff6b35;
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #a0a0a0;
            font-weight: 500;
            font-size: 15px;
            transition: color 0.2s ease;
        }

        .nav-links a:hover {
            color: #ffffff;
        }

        .nav-dropdown {
            position: relative;
        }

        .nav-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 8px 0;
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .dropdown-menu a {
            display: block;
            padding: 8px 16px;
            color: #a0a0a0;
            font-size: 14px;
        }

        .dropdown-menu a:hover {
            background: #2a2a2a;
            color: #ffffff;
        }

        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 8px;
        }

        .mobile-menu span {
            width: 20px;
            height: 2px;
            background: #ffffff;
            margin: 2px 0;
            transition: 0.3s;
            border-radius: 1px;
        }

        /* Hero Section */
        .hero {
            padding: 120px 0 100px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 32px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hero-badge:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .hero-badge::before {
            content: '🚀';
            font-size: 16px;
        }

        .hero h1 {
            font-size: 64px;
            font-weight: 700;
            line-height: 1.1;
            color: white;
            margin-bottom: 24px;
            letter-spacing: -0.02em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .hero-subtitle {
            font-size: 24px;
            font-weight: 300;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 16px;
            letter-spacing: 0.5px;
        }

        .hero p {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            max-width: 600px;
            margin: 0 auto 48px;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 80px;
            flex-wrap: wrap;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            font-size: 32px;
            font-weight: 700;
            color: white;
            display: block;
            margin-bottom: 8px;
        }

        .hero-stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: white;
            color: #667eea;
            padding: 16px 32px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-primary::after {
            content: '→';
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .btn-primary:hover::after {
            transform: translateX(4px);
        }

        .btn-secondary {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            padding: 16px 32px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary:hover {
            color: white;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .btn-secondary::before {
            content: '📞';
            font-size: 16px;
        }

        /* Main Features Section */
        .main-features {
            padding: 80px 0;
            background: #111111;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 60px;
            align-items: stretch;
        }

        .feature-item {
            text-align: center;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .feature-item h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .feature-item p {
            color: #666;
            line-height: 1.6;
            font-size: 15px;
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: #f8f9fa;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        /* Secondary Features */
        .secondary-features {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .secondary-features h2 {
            font-size: 32px;
            font-weight: 400;
            color: #1a1a1a;
            text-align: center;
            margin-bottom: 60px;
            letter-spacing: -0.01em;
        }

        .feature-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            margin-bottom: 80px;
        }

        .feature-row:nth-child(even) .feature-content {
            order: 2;
        }

        .feature-row:nth-child(even) .feature-image {
            order: 1;
        }

        .feature-content h3 {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .feature-content p {
            color: #666;
            line-height: 1.6;
            font-size: 16px;
        }

        .feature-image {
            background: #e5e5e5;
            border-radius: 12px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 24px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu {
                display: flex;
            }

            .hero {
                padding: 80px 0 60px;
            }

            .hero h1 {
                font-size: 42px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .hero p {
                font-size: 16px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 16px;
            }

            .hero-stats {
                gap: 40px;
                margin-top: 60px;
            }

            .hero-stat-number {
                font-size: 24px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .feature-item {
                height: auto !important;
            }

            .feature-row {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .feature-row:nth-child(even) .feature-content,
            .feature-row:nth-child(even) .feature-image {
                order: unset;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 60px 0 40px;
            }

            .hero h1 {
                font-size: 32px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .hero p {
                font-size: 15px;
            }

            .hero-stats {
                gap: 30px;
                margin-top: 40px;
            }

            .hero-stat-number {
                font-size: 20px;
            }

            .btn-primary, .btn-secondary {
                padding: 14px 28px;
                font-size: 15px;
                width: 100%;
                max-width: 280px;
            }

            .main-features,
            .secondary-features {
                padding: 60px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <a href="/" class="logo">
                <img src="https://img.medsci.cn/202506/24b34b64f8eb4181ae1bca34d28ebf7b-t52jFJPoctgO.png" alt="NovaX" style="height: 32px; filter: brightness(0) invert(1);">
            </a>
            <ul class="nav-links" id="navLinks">
                <li><a href="#features">功能特色</a></li>
                <li><a href="#pricing">定价方案</a></li>
                <li><a href="#faq">常见问题</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
            <div class="mobile-menu" id="mobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <a href="#features" class="hero-badge">全新AI科研助手正式发布</a>

                <div style="margin-bottom: 40px;">
                    <img src="https://static.medsci.cn/ai-write/NovaX-0612.webp" alt="NovaX AI" style="max-width: 200px; height: auto; margin-bottom: 20px; border-radius: 12px;">
                    <br>
                    <img src="https://img.medsci.cn/202506/24b34b64f8eb4181ae1bca34d28ebf7b-t52jFJPoctgO.png" alt="NovaX Logo" style="max-width: 300px; height: auto; filter: brightness(0) invert(1);">
                </div>

                <h1>您的灵感引擎</h1>
                <div class="hero-subtitle">构建前瞻性研究蓝图</div>
                <p>基于先进AI技术，为科研工作者提供个性化研究规划、战略性课题集群和前瞻性领域洞察，助您在学术道路上抢占先机，实现突破性创新。</p>

                <div class="hero-buttons">
                    <a href="#pricing" class="btn-primary">免费开始体验</a>
                    <a href="#contact" class="btn-secondary">预约专家咨询</a>
                </div>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="hero-stat-number">10,000+</span>
                        <span class="hero-stat-label">科研工作者</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">50+</span>
                        <span class="hero-stat-label">研究领域</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">95%</span>
                        <span class="hero-stat-label">满意度</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number">24/7</span>
                        <span class="hero-stat-label">智能服务</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="main-features" id="pricing">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 20px; color: #ffffff;">选择最适合您的NovaX方案</h2>
            <p style="text-align: center; color: #a0a0a0; font-size: 18px; max-width: 800px; margin: 0 auto;">根据您的需求选择最适合的评估方案，每一种方案都为您提供专业的评估和优化建议</p>

            <div class="feature-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; align-items: stretch;">
                <div class="feature-item" style="border: 1px solid #333; border-radius: 12px; padding: 32px; background: #1a1a1a; display: flex; flex-direction: column; height: 100%;">
                    <div style="margin-bottom: 20px;">
                        <img src="https://img.medsci.cn/202506/a10c21288b4644dab4ecc620c3e21986-48h98jj9dG92.png" alt="NovaX Base" style="width: 60px; height: 60px; margin: 0 auto 16px; display: block; filter: brightness(0) invert(1);">
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 8px;">NovaX Base</h3>
                    <p style="color: #a0a0a0; font-size: 14px; margin-bottom: 20px;">核心思路启发</p>
                    <ul style="text-align: left; margin-bottom: 24px; padding-left: 0; flex-grow: 1;">
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5;">基于您提供的研究背景或关注领域，进行单次科研方向思路设计</li>
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5;">获取创新思路的初步构想，点亮您的研究起点</li>
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5; opacity: 0;">占位文本保持高度一致</li>
                    </ul>
                    <div style="margin-top: auto;">
                        <div style="font-size: 24px; font-weight: 600; color: #10b981; margin-bottom: 16px;">免费使用</div>
                        <button class="btn-primary" onclick="openTrialModal('base')" style="width: 100%; background: #2563eb; color: white;">立即体验</button>
                    </div>
                </div>

                <div class="feature-item" style="border: 2px solid #4f46e5; border-radius: 12px; padding: 32px; background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%); position: relative; display: flex; flex-direction: column; height: 100%;">
                    <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: #4f46e5; color: white; padding: 4px 16px; border-radius: 12px; font-size: 12px; font-weight: 600;">推荐</div>
                    <div style="margin-bottom: 20px;">
                        <img src="https://img.medsci.cn/202506/1d2c4ec53146414786d7c9203471d8a0-XXNIyugBoaR0.png" alt="NovaX Pro" style="width: 60px; height: 60px; margin: 0 auto 16px; display: block; filter: brightness(0) invert(1);">
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 8px;">NovaX Pro</h3>
                    <p style="color: #c7d2fe; font-size: 14px; margin-bottom: 20px;">个性化研究规划</p>
                    <ul style="text-align: left; margin-bottom: 24px; padding-left: 0; flex-grow: 1;">
                        <li style="margin-bottom: 8px; color: #c7d2fe; font-size: 14px; line-height: 1.5;">基于您当前的研究基础与可用条件，进行深度匹配</li>
                        <li style="margin-bottom: 8px; color: #c7d2fe; font-size: 14px; line-height: 1.5;">提供个性化的研究设计方案与合理的科研实施规划</li>
                        <li style="margin-bottom: 8px; color: #c7d2fe; font-size: 14px; line-height: 1.5;">智能匹配潜在的目标平台与发展机遇</li>
                    </ul>
                    <div style="margin-top: auto;">
                        <div style="font-size: 24px; font-weight: 600; color: #ffffff; margin-bottom: 16px;">联系咨询</div>
                        <button class="btn-primary" onclick="openTrialModal('pro')" style="width: 100%; background: #4f46e5; color: white;">了解 Pro 版详情</button>
                    </div>
                </div>

                <div class="feature-item" style="border: 1px solid #333; border-radius: 12px; padding: 32px; background: #1a1a1a; display: flex; flex-direction: column; height: 100%;">
                    <div style="margin-bottom: 20px;">
                        <img src="https://img.medsci.cn/202506/d2670cf0106346df8d572a543e796120-Oelef8ad32W9.png" alt="NovaX Ultra" style="width: 60px; height: 60px; margin: 0 auto 16px; display: block; filter: brightness(0) invert(1);">
                    </div>
                    <h3 style="color: #ffffff; margin-bottom: 8px;">NovaX Ultra</h3>
                    <p style="color: #a0a0a0; font-size: 14px; margin-bottom: 20px;">擘画科研未来，构筑创新生态</p>
                    <ul style="text-align: left; margin-bottom: 24px; padding-left: 0; flex-grow: 1;">
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5;">战略性课题集群：超越单一思路，智能规划多点联动的系列创新研究课题群</li>
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5;">前瞻性领域洞察：融合新兴技术与交叉学科趋势，提供未来3~5年高潜力研究方向</li>
                        <li style="margin-bottom: 8px; color: #a0a0a0; font-size: 14px; line-height: 1.5;">深度定制与一站式方案：从颠覆性灵感孵化到完整战略方案构建</li>
                    </ul>
                    <div style="margin-top: auto;">
                        <div style="font-size: 24px; font-weight: 600; color: #ffffff; margin-bottom: 16px;">专属定制</div>
                        <button class="btn-primary" onclick="openTrialModal('ultra')" style="width: 100%; background: #2563eb; color: white;">了解 Ultra 专属特权</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" style="padding: 80px 0; background: #1a1a1a;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px; color: #ffffff;">为什么选择 NovaX AI</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px;">
                <div style="text-align: center; padding: 40px 20px;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">🧠</div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: #ffffff;">智能思路生成</h3>
                    <p style="color: #a0a0a0; line-height: 1.6;">基于您的研究背景和领域，AI 智能生成创新的科研思路和方向建议。</p>
                </div>

                <div style="text-align: center; padding: 40px 20px;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">📊</div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: #ffffff;">个性化规划</h3>
                    <p style="color: #a0a0a0; line-height: 1.6;">根据您的研究基础和可用条件，提供深度匹配的个性化研究设计方案。</p>
                </div>

                <div style="text-align: center; padding: 40px 20px;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px;">🚀</div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px; color: #ffffff;">前瞻性洞察</h3>
                    <p style="color: #a0a0a0; line-height: 1.6;">融合新兴技术与交叉学科趋势，提供未来3-5年的高潜力研究方向。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section style="padding: 80px 0; background: #ffffff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">企业级安全和隐私保护</h2>
            <p style="color: #666; font-size: 18px; max-width: 600px; margin: 0 auto;">您可以放心地交付最佳工作。</p>
        </div>
    </section>

    <!-- Additional Features */
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">您需要的一切</h2>

            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 60px; margin-bottom: 60px;">
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">自动驾驶模式</h3>
                    <p style="color: #666; line-height: 1.6;">让 Kiro 自主运行大型任务，无需逐步指导。您始终保持控制，特别是在运行脚本或命令时。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">您的代码，您的规则</h3>
                    <p style="color: #666; line-height: 1.6;">通过引导文件配置 Kiro 代理与每个项目的交互方式。添加上下文、编码标准、首选工作流程或工具。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">最先进的技术驱动</h3>
                    <p style="color: #666; line-height: 1.6;">选择 Claude Sonnet 3.7 或 Sonnet 4 模型，更多选项即将推出。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">与 VS Code 兼容</h3>
                    <p style="color: #666; line-height: 1.6;">Kiro 在简化的 AI 就绪环境中支持 Open VSX 插件、主题和 VS Code 设置。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">展示，而非讲述</h3>
                    <p style="color: #666; line-height: 1.6;">放入您的 UI 设计图片或架构白板会议照片，Kiro 可以使用它来指导实现。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">见证代码差异的魔力</h3>
                    <p style="color: #666; line-height: 1.6;">实时查看代码更改。批准所有内容、逐步检查每个更改，或一键编辑。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="padding: 80px 0; background: #ffffff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">开启您的科研创新之旅</h2>
            <p style="color: #666; font-size: 18px; margin-bottom: 40px;">NovaX AI 助您在学术道路上实现突破性进展</p>
            <a href="#pricing" class="btn-primary">立即体验</a>
        </div>
    </section>

    <!-- Testimonials -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">受到全球工程师信赖</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 40px;">
                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"我被 Kiro 的能力震撼了。智能体验真正具有变革性。从理解上下文的多模态输入到 IDE 内的完整生命周期控制，感觉就像在与一位高级开发人员合作。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Vivek Velso</div>
                            <div style="color: #666; font-size: 14px;">云工程负责人</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"大多数工具擅长生成代码，但 Kiro 在您编写第一行代码之前就为混乱带来了结构。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Farah Abdirahman</div>
                            <div style="color: #666; font-size: 14px;">云与AI工程师</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"大约两天内，我从零开始构建了一个安全的文件共享应用程序。只需与 Kiro 分享我的需求，我就能创建一个包含加密和各种安全编码实践的完全安全应用程序。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Ihor Sasovets</div>
                            <div style="color: #666; font-size: 14px;">首席安全工程师</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Guides -->
    <section style="padding: 80px 0; background: #ffffff;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">快速入门指南</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 32px;">
                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        教程图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #0369a1; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">教程</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">边玩边学：创建视频游戏</h3>
                    </div>
                </div>

                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        活动图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #dc2626; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">活动</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">Kiro 编程马拉松</h3>
                    </div>
                </div>

                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        指南图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #059669; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">指南</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">从 VS Code 迁移</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">常见问题</h2>

            <div style="max-width: 800px; margin: 0 auto;">
                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>NovaX AI 如何帮助我的科研工作？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        NovaX AI 通过分析您的研究背景和领域，为您提供创新的科研思路、个性化的研究设计方案，以及战略性的课题规划，帮助您在科研道路上找到突破点和发展方向。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>三个版本的主要区别是什么？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        Base版提供基础的思路启发，Pro版提供个性化研究规划和深度匹配，Ultra版则提供战略性课题集群和前瞻性领域洞察，适合不同层次的科研需求。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>如何申请试用？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        您可以点击相应版本的按钮，填写申请表单，包括邮箱、单位、职称和手机号等信息。我们会尽快联系您安排试用。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>NovaX AI 适用于哪些研究领域？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        NovaX AI 适用于医学、生物学、工程学、计算机科学、社会科学等多个研究领域，能够根据不同学科的特点提供针对性的研究建议。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>服务的响应时间是多久？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        我们的企业微信工作日9:00-18:00在线为您服务，试用申请通常在1-2个工作日内得到回复，具体服务时间根据所选版本和需求复杂度而定。
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" style="padding: 80px 0; background: #1a1a1a; color: white; text-align: center;">
        <div class="container">
            <div style="margin-bottom: 40px;">
                <img src="https://img.medsci.cn/202506/a75d550504434d22aacaefcc951bc9ec-PBB8bYHyNwIQ.png" alt="联系我们" style="max-width: 200px; height: auto; margin-bottom: 20px;">
            </div>
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">联系我们</h2>
            <p style="font-size: 18px; margin-bottom: 20px; opacity: 0.8;"><EMAIL></p>
            <p style="font-size: 16px; margin-bottom: 40px; opacity: 0.8;">扫码添加企业微信，工作日9:00-18:00在线为您服务</p>
            <button onclick="showQRCode()" style="background: white; color: #1a1a1a; padding: 12px 24px; border-radius: 8px; border: none; font-weight: 500; font-size: 16px; cursor: pointer; transition: all 0.2s ease;">扫码联系</button>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: #f8f9fa; padding: 60px 0 24px; border-top: 1px solid #e5e5e5;">
        <div class="container">
            <div style="text-align: center;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 24px;">
                    <span style="font-size: 24px; font-weight: 600; color: #1a1a1a;">NovaX AI</span>
                </div>
                <p style="color: #666; margin-bottom: 20px;">您的灵感引擎，构建前瞻性研究蓝图</p>
                <p style="color: #666; font-size: 14px;">© 2025 NovaX. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenu = document.getElementById('mobileMenu');
        const navLinks = document.getElementById('navLinks');

        mobileMenu.addEventListener('click', () => {
            navLinks.classList.toggle('active');

            // Animate hamburger menu
            const spans = mobileMenu.querySelectorAll('span');
            if (navLinks.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });

        // FAQ toggle function
        function toggleFaq(element) {
            const faqItem = element.parentElement;
            const answer = element.nextElementSibling;
            const icon = element.querySelector('.faq-icon');

            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (item !== faqItem) {
                    item.classList.remove('active');
                    item.querySelector('.faq-answer').style.display = 'none';
                    item.querySelector('.faq-icon').textContent = '+';
                    item.querySelector('.faq-icon').style.transform = 'none';
                }
            });

            // Toggle current FAQ item
            if (faqItem.classList.contains('active')) {
                faqItem.classList.remove('active');
                answer.style.display = 'none';
                icon.textContent = '+';
                icon.style.transform = 'none';
            } else {
                faqItem.classList.add('active');
                answer.style.display = 'block';
                icon.textContent = '+';
                icon.style.transform = 'rotate(45deg)';
            }
        };

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.8)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.05)';
            }

            lastScrollY = currentScrollY;
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.feature-item, .feature-row, .faq-item').forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
            observer.observe(element);
        });

        // Add hover effects to cards and buttons
        document.querySelectorAll('.btn-primary').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-2px)';
                btn.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = 'none';
            });
        });

        // Add responsive behavior for mobile
        function handleResize() {
            if (window.innerWidth <= 768) {
                // Mobile specific adjustments
                document.querySelectorAll('.feature-row').forEach(row => {
                    row.style.gridTemplateColumns = '1fr';
                    row.style.gap = '40px';
                });
            } else {
                // Desktop specific adjustments
                document.querySelectorAll('.feature-row').forEach(row => {
                    row.style.gridTemplateColumns = '1fr 1fr';
                    row.style.gap = '80px';
                });
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // Initial call

        // Trial modal functions
        function openTrialModal(version) {
            document.getElementById('trialModal').style.display = 'flex';
            document.getElementById('selectedVersion').textContent = 'Novax ' + version.charAt(0).toUpperCase() + version.slice(1);
        }

        function closeTrialModal() {
            document.getElementById('trialModal').style.display = 'none';
            document.getElementById('trialForm').reset();
        }

        function submitTrial() {
            const form = document.getElementById('trialForm');
            const formData = new FormData(form);

            // 简单的表单验证
            const email = formData.get('email');
            const company = formData.get('company');
            const title = formData.get('title');
            const phone = formData.get('phone');

            if (!email || !company || !title || !phone) {
                alert('请填写所有必填字段');
                return;
            }

            if (!email.includes('@')) {
                alert('请输入有效的邮箱地址');
                return;
            }

            // 显示成功消息
            document.getElementById('trialModal').style.display = 'none';
            document.getElementById('successModal').style.display = 'flex';
        }

        function closeSuccessModal() {
            document.getElementById('successModal').style.display = 'none';
        }

        function showQRCode() {
            document.getElementById('qrModal').style.display = 'flex';
        }

        function closeQRModal() {
            document.getElementById('qrModal').style.display = 'none';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const trialModal = document.getElementById('trialModal');
            const successModal = document.getElementById('successModal');
            const qrModal = document.getElementById('qrModal');

            if (event.target === trialModal) {
                closeTrialModal();
            }
            if (event.target === successModal) {
                closeSuccessModal();
            }
            if (event.target === qrModal) {
                closeQRModal();
            }
        }
    </script>

    <!-- Trial Application Modal -->
    <div id="trialModal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); justify-content: center; align-items: center;">
        <div style="background: white; padding: 40px; border-radius: 12px; max-width: 500px; width: 90%; max-height: 90%; overflow-y: auto;">
            <h3 style="margin-bottom: 20px; font-size: 24px; color: #1a1a1a;">请选择试用版本</h3>
            <p style="margin-bottom: 30px; color: #666;">选择最适合您的版本开始试用</p>

            <form id="trialForm">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #1a1a1a;">试用版本</label>
                    <div id="selectedVersion" style="padding: 12px; background: #f8f9fa; border-radius: 6px; color: #666;">Novax Base</div>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #1a1a1a;">*邮箱</label>
                    <input type="email" name="email" required style="width: 100%; padding: 12px; border: 1px solid #e5e5e5; border-radius: 6px; font-size: 16px;" placeholder="请输入有效的邮箱地址">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #1a1a1a;">*单位</label>
                    <input type="text" name="company" required style="width: 100%; padding: 12px; border: 1px solid #e5e5e5; border-radius: 6px; font-size: 16px;" placeholder="请输入您的单位名称">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #1a1a1a;">*职称</label>
                    <input type="text" name="title" required style="width: 100%; padding: 12px; border: 1px solid #e5e5e5; border-radius: 6px; font-size: 16px;" placeholder="请输入您的职称">
                </div>

                <div style="margin-bottom: 30px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #1a1a1a;">*手机号</label>
                    <input type="tel" name="phone" required style="width: 100%; padding: 12px; border: 1px solid #e5e5e5; border-radius: 6px; font-size: 16px;" placeholder="请输入有效的手机号">
                </div>

                <p style="font-size: 12px; color: #999; margin-bottom: 20px;">*申请版本并非最终试用版本</p>

                <div style="display: flex; gap: 12px;">
                    <button type="button" onclick="closeTrialModal()" style="flex: 1; padding: 12px; background: #f8f9fa; color: #666; border: 1px solid #e5e5e5; border-radius: 6px; font-size: 16px; cursor: pointer;">取消</button>
                    <button type="button" onclick="submitTrial()" style="flex: 1; padding: 12px; background: #1a1a1a; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">提交申请</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); justify-content: center; align-items: center;">
        <div style="background: white; padding: 40px; border-radius: 12px; max-width: 400px; width: 90%; text-align: center;">
            <h3 style="margin-bottom: 20px; font-size: 24px; color: #10b981;">申请提交成功！</h3>
            <p style="margin-bottom: 30px; color: #666;">试用申请已提交。我们会尽快联系您，请耐心等待...</p>
            <button onclick="closeSuccessModal()" style="padding: 12px 24px; background: #1a1a1a; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">确定</button>
        </div>
    </div>

    <!-- QR Code Modal -->
    <div id="qrModal" style="display: none; position: fixed; z-index: 2000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); justify-content: center; align-items: center;">
        <div style="background: white; padding: 40px; border-radius: 12px; max-width: 400px; width: 90%; text-align: center;">
            <button onclick="closeQRModal()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">×</button>
            <h3 style="margin-bottom: 20px; font-size: 20px; color: #1a1a1a;">扫码添加企业微信</h3>
            <div style="width: 200px; height: 200px; background: #f0f0f0; margin: 0 auto 20px; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999;">
                企业微信二维码
            </div>
            <p style="color: #666; font-size: 14px;">扫描上方二维码，添加我们的企业微信<br>工作日9:00-18:00在线为您服务</p>
        </div>
    </div>
</body>
</html>
