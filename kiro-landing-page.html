<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Kiro: 从原型到生产的AI IDE。通过规范驱动开发为AI编码带来结构化，帮助您完成最佳工作。">
    <meta name="keywords" content="AI IDE,人工智能开发环境,规范驱动开发,代码生成,智能编程助手">
    <meta name="author" content="Kiro">
    <meta property="og:title" content="Kiro: 从原型到生产的AI IDE">
    <meta property="og:description" content="通过规范驱动开发为AI编码带来结构化，帮助您完成最佳工作。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kiro.dev">
    <meta property="og:image" content="/images/og-image.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Kiro: 从原型到生产的AI IDE">
    <meta name="twitter:description" content="通过规范驱动开发为AI编码带来结构化，帮助您完成最佳工作。">
    <title>Kiro: 从原型到生产的AI IDE</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.5;
            color: #1a1a1a;
            background-color: #ffffff;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header */
        header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            height: 72px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            text-decoration: none;
        }

        .logo-text {
            margin-right: 8px;
        }

        .preview-badge {
            background: #ff6b35;
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            font-size: 15px;
            transition: color 0.2s ease;
        }

        .nav-links a:hover {
            color: #1a1a1a;
        }

        .nav-dropdown {
            position: relative;
        }

        .nav-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 8px 0;
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .dropdown-menu a {
            display: block;
            padding: 8px 16px;
            color: #666;
            font-size: 14px;
        }

        .dropdown-menu a:hover {
            background: #f8f9fa;
            color: #1a1a1a;
        }

        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 8px;
        }

        .mobile-menu span {
            width: 20px;
            height: 2px;
            background: #1a1a1a;
            margin: 2px 0;
            transition: 0.3s;
            border-radius: 1px;
        }

        /* Hero Section */
        .hero {
            padding: 80px 0 60px;
            background: #ffffff;
            text-align: center;
        }

        .hero-badge {
            display: inline-block;
            background: #f0f9ff;
            color: #0369a1;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 24px;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .hero-badge:hover {
            background: #e0f2fe;
        }

        .hero h1 {
            font-size: 56px;
            font-weight: 400;
            line-height: 1.1;
            color: #1a1a1a;
            margin-bottom: 24px;
            letter-spacing: -0.02em;
        }

        .hero p {
            font-size: 20px;
            color: #666;
            max-width: 700px;
            margin: 0 auto 40px;
            line-height: 1.5;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #333;
            transform: translateY(-1px);
        }

        .btn-secondary {
            color: #666;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.2s ease;
        }

        .btn-secondary:hover {
            color: #1a1a1a;
        }

        /* Main Features Section */
        .main-features {
            padding: 80px 0;
            background: #ffffff;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 60px;
        }

        .feature-item {
            text-align: center;
        }

        .feature-item h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .feature-item p {
            color: #666;
            line-height: 1.6;
            font-size: 15px;
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: #f8f9fa;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        /* Secondary Features */
        .secondary-features {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .secondary-features h2 {
            font-size: 32px;
            font-weight: 400;
            color: #1a1a1a;
            text-align: center;
            margin-bottom: 60px;
            letter-spacing: -0.01em;
        }

        .feature-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            margin-bottom: 80px;
        }

        .feature-row:nth-child(even) .feature-content {
            order: 2;
        }

        .feature-row:nth-child(even) .feature-image {
            order: 1;
        }

        .feature-content h3 {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .feature-content p {
            color: #666;
            line-height: 1.6;
            font-size: 16px;
        }

        .feature-image {
            background: #e5e5e5;
            border-radius: 12px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 24px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu {
                display: flex;
            }

            .hero h1 {
                font-size: 36px;
            }

            .hero p {
                font-size: 18px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .feature-row {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .feature-row:nth-child(even) .feature-content,
            .feature-row:nth-child(even) .feature-image {
                order: unset;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 60px 0 40px;
            }

            .hero h1 {
                font-size: 28px;
            }

            .hero p {
                font-size: 16px;
            }

            .main-features,
            .secondary-features {
                padding: 60px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <a href="/" class="logo">
                <span class="logo-text">Kiro</span>
                <span class="preview-badge">PREVIEW</span>
            </a>
            <ul class="nav-links" id="navLinks">
                <li><a href="/changelog/">更新日志</a></li>
                <li><a href="/pricing/">定价</a></li>
                <li><a href="/docs/">文档</a></li>
                <li class="nav-dropdown">
                    <a href="#">资源</a>
                    <div class="dropdown-menu">
                        <a href="/downloads/">下载</a>
                        <a href="/blog/">博客</a>
                        <a href="/faq/">常见问题</a>
                    </div>
                </li>
            </ul>
            <div class="mobile-menu" id="mobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <a href="/blog/introducing-kiro/" class="hero-badge">介绍 Kiro</a>
            <h1>从原型到生产的AI IDE</h1>
            <p>Kiro 通过规范驱动开发为 AI 编码带来结构化，帮助您完成最佳工作。</p>
            <div class="hero-buttons">
                <a href="/downloads/" class="btn-primary">下载</a>
                <a href="#demo" class="btn-secondary">观看演示</a>
            </div>
        </div>
    </section>

    <!-- Main Features -->
    <section class="main-features">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 20px;">通过规范驱动开发驯服复杂性</h2>
            <p style="text-align: center; color: #666; font-size: 18px; max-width: 600px; margin: 0 auto;">Kiro 将您的提示转化为清晰的需求、系统设计和离散任务。</p>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">📋</div>
                    <h3>协作制定规范和架构</h3>
                    <p>与 Kiro 协作制定您的规范和架构设计，确保项目方向清晰明确。</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🤖</div>
                    <h3>智能代理实现规范</h3>
                    <p>Kiro 代理在您的控制下实现规范，保持开发过程的可控性。</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <h3>代理钩子自动化任务</h3>
                    <p>将任务委托给在事件触发时自动执行的 AI 代理，如"文件保存"。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Secondary Features -->
    <section class="secondary-features">
        <div class="container">
            <h2>从随意编码到可行代码</h2>

            <div class="feature-row">
                <div class="feature-content">
                    <h3>通过规范驱动开发为 AI 编码带来结构</h3>
                    <p>Kiro 将您的提示转化为清晰的需求、结构化设计、经过强大测试验证的实现任务，以及由高级代理生成的代码。这是 AI 编码的流程，通过成熟的工程实践得到提升。</p>
                </div>
                <div class="feature-image">
                    <span>Kiro 任务列表界面截图</span>
                </div>
            </div>

            <div class="feature-row">
                <div class="feature-content">
                    <h3>从头开始为与代理协作而构建</h3>
                    <p>多模态聊天、规范驱动开发、代理钩子 - Kiro 为您提供最佳工具，在熟悉而又全新的开发体验中完成工作。</p>
                </div>
                <div class="feature-image">
                    <span>Kiro 代码差异视图截图</span>
                </div>
            </div>

            <div class="feature-row">
                <div class="feature-content">
                    <h3>更多上下文，更少重复</h3>
                    <p>通过规范、引导和智能上下文管理，Kiro 理解您提示背后的意图，帮助您在更大的代码库上实现复杂功能，用更少的尝试。</p>
                </div>
                <div class="feature-image">
                    <span>Kiro 文件浏览界面截图</span>
                </div>
            </div>

            <div class="feature-row">
                <div class="feature-content">
                    <h3>通过 MCP 集成工具和数据</h3>
                    <p>通过原生 MCP 集成连接到文档、数据库、API 等，让您可以在工作的地方引入您的世界。</p>
                </div>
                <div class="feature-image">
                    <span>Kiro MCP 服务器设置截图</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section style="padding: 80px 0; background: #ffffff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">企业级安全和隐私保护</h2>
            <p style="color: #666; font-size: 18px; max-width: 600px; margin: 0 auto;">您可以放心地交付最佳工作。</p>
        </div>
    </section>

    <!-- Additional Features */
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">您需要的一切</h2>

            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 60px; margin-bottom: 60px;">
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">自动驾驶模式</h3>
                    <p style="color: #666; line-height: 1.6;">让 Kiro 自主运行大型任务，无需逐步指导。您始终保持控制，特别是在运行脚本或命令时。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">您的代码，您的规则</h3>
                    <p style="color: #666; line-height: 1.6;">通过引导文件配置 Kiro 代理与每个项目的交互方式。添加上下文、编码标准、首选工作流程或工具。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">最先进的技术驱动</h3>
                    <p style="color: #666; line-height: 1.6;">选择 Claude Sonnet 3.7 或 Sonnet 4 模型，更多选项即将推出。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">与 VS Code 兼容</h3>
                    <p style="color: #666; line-height: 1.6;">Kiro 在简化的 AI 就绪环境中支持 Open VSX 插件、主题和 VS Code 设置。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">展示，而非讲述</h3>
                    <p style="color: #666; line-height: 1.6;">放入您的 UI 设计图片或架构白板会议照片，Kiro 可以使用它来指导实现。</p>
                </div>
                <div>
                    <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 16px;">见证代码差异的魔力</h3>
                    <p style="color: #666; line-height: 1.6;">实时查看代码更改。批准所有内容、逐步检查每个更改，或一键编辑。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="padding: 80px 0; background: #ffffff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">免费开始使用</h2>
            <p style="color: #666; font-size: 18px; margin-bottom: 40px;">Kiro 在预览期间免费使用</p>
            <a href="/downloads/" class="btn-primary">下载</a>
        </div>
    </section>

    <!-- Testimonials -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">受到全球工程师信赖</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 40px;">
                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"我被 Kiro 的能力震撼了。智能体验真正具有变革性。从理解上下文的多模态输入到 IDE 内的完整生命周期控制，感觉就像在与一位高级开发人员合作。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Vivek Velso</div>
                            <div style="color: #666; font-size: 14px;">云工程负责人</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"大多数工具擅长生成代码，但 Kiro 在您编写第一行代码之前就为混乱带来了结构。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Farah Abdirahman</div>
                            <div style="color: #666; font-size: 14px;">云与AI工程师</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; padding: 32px; border-radius: 12px; border: 1px solid #e5e5e5;">
                    <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-style: italic;">"大约两天内，我从零开始构建了一个安全的文件共享应用程序。只需与 Kiro 分享我的需求，我就能创建一个包含加密和各种安全编码实践的完全安全应用程序。"</p>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 40px; height: 40px; background: #e5e5e5; border-radius: 50%;"></div>
                        <div>
                            <div style="font-weight: 600; color: #1a1a1a;">Ihor Sasovets</div>
                            <div style="color: #666; font-size: 14px;">首席安全工程师</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Guides -->
    <section style="padding: 80px 0; background: #ffffff;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">快速入门指南</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 32px;">
                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        教程图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #0369a1; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">教程</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">边玩边学：创建视频游戏</h3>
                    </div>
                </div>

                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        活动图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #dc2626; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">活动</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">Kiro 编程马拉松</h3>
                    </div>
                </div>

                <div style="border: 1px solid #e5e5e5; border-radius: 12px; overflow: hidden; transition: all 0.2s ease;">
                    <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; color: #999;">
                        指南图片
                    </div>
                    <div style="padding: 24px;">
                        <div style="color: #059669; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px;">指南</div>
                        <h3 style="font-size: 18px; font-weight: 600; color: #1a1a1a;">从 VS Code 迁移</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <h2 style="text-align: center; font-size: 32px; font-weight: 400; margin-bottom: 60px;">常见问题</h2>

            <div style="max-width: 800px; margin: 0 auto;">
                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>什么是规范驱动开发？它与随意编码有何不同？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        规范驱动开发是一种结构化的开发方法，它要求在编码之前先定义清晰的需求、架构和实现计划。与随意编码不同，它提供了明确的方向和可验证的目标，确保代码质量和项目成功。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>如何开始使用 Kiro？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        您可以从我们的下载页面免费下载 Kiro。安装后，您可以立即开始创建项目，Kiro 会引导您完成规范驱动开发的整个流程。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>Kiro 支持哪些编程语言？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        Kiro 支持所有主流编程语言，包括 JavaScript、Python、Java、C++、Go、Rust 等。我们的 AI 代理经过训练，能够理解和生成高质量的多语言代码。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>我可以用哪些语言提问？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        Kiro 支持多种自然语言，包括中文、英文、日文、韩文等。您可以用您最舒适的语言与 Kiro 交流，它会理解您的意图并提供相应的帮助。
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e5e5; margin-bottom: 0;">
                    <div class="faq-question" onclick="toggleFaq(this)" style="padding: 24px 0; font-weight: 600; font-size: 18px; color: #1a1a1a; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: color 0.2s ease;">
                        <span>我可以从现有 IDE 导入设置吗？</span>
                        <span class="faq-icon" style="font-size: 18px; transition: transform 0.2s ease;">+</span>
                    </div>
                    <div class="faq-answer" style="padding-bottom: 24px; color: #666; font-size: 16px; line-height: 1.6; display: none;">
                        是的，Kiro 完全兼容 VS Code 的设置、插件和主题。您可以轻松导入现有的配置，无缝过渡到 Kiro 的 AI 增强开发环境。
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="/faq/" style="color: #0369a1; text-decoration: none; font-weight: 500;">浏览所有常见问题</a>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section style="padding: 80px 0; background: #1a1a1a; color: white; text-align: center;">
        <div class="container">
            <h2 style="font-size: 32px; font-weight: 400; margin-bottom: 20px;">几分钟内构建真实项目</h2>
            <p style="font-size: 18px; margin-bottom: 40px; opacity: 0.8;">Kiro 在预览期间免费使用</p>
            <a href="/downloads/" style="background: white; color: #1a1a1a; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 500; font-size: 16px; transition: all 0.2s ease; display: inline-block;">下载</a>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: #f8f9fa; padding: 60px 0 24px; border-top: 1px solid #e5e5e5;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 48px; margin-bottom: 48px;">
                <div>
                    <div style="display: flex; align-items: center; margin-bottom: 24px;">
                        <span style="font-size: 24px; font-weight: 600; color: #1a1a1a;">Kiro</span>
                    </div>
                </div>
                <div>
                    <h3 style="font-size: 14px; font-weight: 600; color: #1a1a1a; margin-bottom: 16px; text-transform: uppercase; letter-spacing: 0.5px;">产品</h3>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 8px;"><a href="/about/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">关于 Kiro</a></li>
                        <li style="margin-bottom: 8px;"><a href="/pricing/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">定价</a></li>
                        <li style="margin-bottom: 8px;"><a href="/changelog/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">更新日志</a></li>
                        <li style="margin-bottom: 8px;"><a href="/downloads/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">下载</a></li>
                    </ul>
                </div>
                <div>
                    <h3 style="font-size: 14px; font-weight: 600; color: #1a1a1a; margin-bottom: 16px; text-transform: uppercase; letter-spacing: 0.5px;">资源</h3>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 8px;"><a href="/docs/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">文档</a></li>
                        <li style="margin-bottom: 8px;"><a href="/blog/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">博客</a></li>
                        <li style="margin-bottom: 8px;"><a href="/faq/" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">常见问题</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">提交反馈</a></li>
                    </ul>
                </div>
                <div>
                    <h3 style="font-size: 14px; font-weight: 600; color: #1a1a1a; margin-bottom: 16px; text-transform: uppercase; letter-spacing: 0.5px;">社交</h3>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">Discord</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">LinkedIn</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">Twitter</a></li>
                        <li style="margin-bottom: 8px;"><a href="#" style="color: #666; text-decoration: none; font-size: 14px; transition: color 0.2s ease;">YouTube</a></li>
                    </ul>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 24px; border-top: 1px solid #e5e5e5; flex-wrap: wrap; gap: 16px;">
                <div style="display: flex; align-items: center; gap: 16px;">
                    <img src="https://via.placeholder.com/80x20/f0f0f0/666?text=AWS" alt="AWS" style="height: 20px;">
                </div>
                <div style="display: flex; gap: 24px; flex-wrap: wrap;">
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">网站条款</a>
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">许可证</a>
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">负责任的AI政策</a>
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">法律</a>
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">隐私政策</a>
                    <a href="#" style="color: #666; text-decoration: none; font-size: 12px;">Cookie偏好</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenu = document.getElementById('mobileMenu');
        const navLinks = document.getElementById('navLinks');

        mobileMenu.addEventListener('click', () => {
            navLinks.classList.toggle('active');

            // Animate hamburger menu
            const spans = mobileMenu.querySelectorAll('span');
            if (navLinks.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });

        // FAQ toggle function
        function toggleFaq(element) {
            const faqItem = element.parentElement;
            const answer = element.nextElementSibling;
            const icon = element.querySelector('.faq-icon');

            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (item !== faqItem) {
                    item.classList.remove('active');
                    item.querySelector('.faq-answer').style.display = 'none';
                    item.querySelector('.faq-icon').textContent = '+';
                    item.querySelector('.faq-icon').style.transform = 'none';
                }
            });

            // Toggle current FAQ item
            if (faqItem.classList.contains('active')) {
                faqItem.classList.remove('active');
                answer.style.display = 'none';
                icon.textContent = '+';
                icon.style.transform = 'none';
            } else {
                faqItem.classList.add('active');
                answer.style.display = 'block';
                icon.textContent = '+';
                icon.style.transform = 'rotate(45deg)';
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.8)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.05)';
            }

            lastScrollY = currentScrollY;
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.feature-item, .feature-row, .faq-item').forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
            observer.observe(element);
        });

        // Add hover effects to cards and buttons
        document.querySelectorAll('.btn-primary').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-2px)';
                btn.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = 'none';
            });
        });

        // Add responsive behavior for mobile
        function handleResize() {
            if (window.innerWidth <= 768) {
                // Mobile specific adjustments
                document.querySelectorAll('.feature-row').forEach(row => {
                    row.style.gridTemplateColumns = '1fr';
                    row.style.gap = '40px';
                });
            } else {
                // Desktop specific adjustments
                document.querySelectorAll('.feature-row').forEach(row => {
                    row.style.gridTemplateColumns = '1fr 1fr';
                    row.style.gap = '80px';
                });
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // Initial call
    </script>
</body>
</html>
