<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="解锁产品的全部潜力，灵活的定价方案满足不同需求。免费试用，专业版功能强大，企业版无限可能。">
    <meta name="keywords" content="定价,价格,订阅,免费试用,专业版,企业版">
    <meta name="author" content="Your Company">
    <meta property="og:title" content="定价方案 - 选择适合您的计划">
    <meta property="og:description" content="解锁产品的全部潜力，灵活的定价方案满足不同需求">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yoursite.com/pricing">
    <meta name="twitter:card" content="summary_large_image">
    <title>定价方案 - 选择适合您的计划</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #2563eb;
        }

        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .mobile-menu span {
            width: 25px;
            height: 3px;
            background: #333;
            margin: 3px 0;
            transition: 0.3s;
        }

        /* Hero Section */
        .hero {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Pricing Section */
        .pricing {
            padding: 4rem 0;
            background: #f8fafc;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .pricing-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .pricing-card.featured {
            border: 2px solid #2563eb;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: "推荐";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }

        .plan-badge {
            background: #fbbf24;
            color: #92400e;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 0.5rem;
        }

        .plan-period {
            color: #64748b;
            margin-bottom: 2rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .plan-features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .plan-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .cta-button {
            width: 100%;
            padding: 1rem;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .cta-button:hover {
            background: #1d4ed8;
        }

        .cta-button.secondary {
            background: transparent;
            color: #2563eb;
            border: 2px solid #2563eb;
        }

        .cta-button.secondary:hover {
            background: #2563eb;
            color: white;
        }

        /* FAQ Section */
        .faq {
            padding: 4rem 0;
        }

        .faq h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #1e293b;
        }

        .faq-item {
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .faq-question {
            padding: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-answer {
            padding: 0 1.5rem 1.5rem;
            color: #64748b;
            display: none;
        }

        .faq-answer.active {
            display: block;
        }

        /* Footer */
        footer {
            background: #1e293b;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #f1f5f9;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #94a3b8;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #334155;
            color: #94a3b8;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: white;
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu {
                display: flex;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card.featured {
                transform: none;
            }

            .plan-price {
                font-size: 2.5rem;
            }

            .container {
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .hero {
                padding: 2rem 0;
            }

            .pricing {
                padding: 2rem 0;
            }

            .faq {
                padding: 2rem 0;
            }

            .plan-price {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">YourBrand</div>
            <ul class="nav-links" id="navLinks">
                <li><a href="#home">首页</a></li>
                <li><a href="#features">功能</a></li>
                <li><a href="#pricing">定价</a></li>
                <li><a href="#docs">文档</a></li>
                <li><a href="#contact">联系</a></li>
            </ul>
            <div class="mobile-menu" id="mobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>解锁产品的全部潜力</h1>
            <p>在预览期间，享受合理限制下的免费使用。选择适合您需求的定价方案，开启无限可能。</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="pricing-grid">
                <!-- Free Plan -->
                <div class="pricing-card">
                    <h3 class="plan-name">免费版</h3>
                    <span class="plan-badge">即将推出</span>
                    <div class="plan-price">¥0</div>
                    <div class="plan-period">/月/用户</div>
                    <ul class="plan-features">
                        <li>智能助手功能（每月限制50次交互）</li>
                        <li>基础规格配置</li>
                        <li>代理钩子功能</li>
                        <li>模型上下文协议 (MCP)</li>
                        <li>代理引导功能</li>
                    </ul>
                    <button class="cta-button secondary">免费开始</button>
                </div>

                <!-- Pro Plan -->
                <div class="pricing-card featured">
                    <h3 class="plan-name">专业版</h3>
                    <span class="plan-badge">即将推出</span>
                    <div class="plan-price">¥129</div>
                    <div class="plan-period">/月/用户</div>
                    <ul class="plan-features">
                        <li>包含免费版所有功能</li>
                        <li>增强的智能助手功能（每月1,000次交互）</li>
                        <li>优先技术支持</li>
                        <li>高级分析报告</li>
                        <li>团队协作功能</li>
                    </ul>
                    <button class="cta-button">选择专业版</button>
                </div>

                <!-- Pro+ Plan -->
                <div class="pricing-card">
                    <h3 class="plan-name">企业版</h3>
                    <span class="plan-badge">即将推出</span>
                    <div class="plan-price">¥259</div>
                    <div class="plan-period">/月/用户</div>
                    <ul class="plan-features">
                        <li>包含专业版所有功能</li>
                        <li>无限制智能助手交互（每月3,000次）</li>
                        <li>专属客户经理</li>
                        <li>定制化集成</li>
                        <li>企业级安全保障</li>
                        <li>SLA服务保证</li>
                    </ul>
                    <button class="cta-button">联系销售</button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2>常见定价问题</h2>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>我能免费使用多长时间？</span>
                    <span>+</span>
                </div>
                <div class="faq-answer">
                    在预览期间，您可以无限期免费使用基础功能。我们会在正式发布前至少提前30天通知任何变更。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>预览期间是否有使用限制？</span>
                    <span>+</span>
                </div>
                <div class="faq-answer">
                    是的，免费版每月限制50次智能助手交互，专业版限制1,000次，企业版限制3,000次。这些限制确保服务质量和公平使用。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>预览期结束后会发生什么？</span>
                    <span>+</span>
                </div>
                <div class="faq-answer">
                    预览期结束后，免费版将继续提供基础功能，付费版本将解锁更多高级功能和更高的使用限制。现有用户将享受特殊优惠。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>如何定义智能助手交互？</span>
                    <span>+</span>
                </div>
                <div class="faq-answer">
                    一次智能助手交互是指您向AI助手发送一个请求并收到回复的完整过程。复杂的多轮对话可能计算为多次交互。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>可以购买额外的交互次数吗？</span>
                    <span>+</span>
                </div>
                <div class="faq-answer">
                    目前暂不支持单独购买额外交互次数。如果您需要更多交互，建议升级到更高级别的订阅计划。
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>产品</h3>
                    <ul>
                        <li><a href="#about">关于我们</a></li>
                        <li><a href="#pricing">定价方案</a></li>
                        <li><a href="#changelog">更新日志</a></li>
                        <li><a href="#downloads">下载</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>资源</h3>
                    <ul>
                        <li><a href="#docs">文档</a></li>
                        <li><a href="#blog">博客</a></li>
                        <li><a href="#faq">常见问题</a></li>
                        <li><a href="#feedback">反馈建议</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>社交媒体</h3>
                    <ul>
                        <li><a href="#discord">Discord</a></li>
                        <li><a href="#linkedin">LinkedIn</a></li>
                        <li><a href="#twitter">Twitter</a></li>
                        <li><a href="#youtube">YouTube</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>法律</h3>
                    <ul>
                        <li><a href="#terms">服务条款</a></li>
                        <li><a href="#privacy">隐私政策</a></li>
                        <li><a href="#license">许可证</a></li>
                        <li><a href="#cookies">Cookie设置</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 YourBrand. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenu = document.getElementById('mobileMenu');
        const navLinks = document.getElementById('navLinks');

        mobileMenu.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });

        // FAQ toggle function
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('span:last-child');

            if (answer.classList.contains('active')) {
                answer.classList.remove('active');
                icon.textContent = '+';
            } else {
                // Close all other FAQ items
                document.querySelectorAll('.faq-answer').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelectorAll('.faq-question span:last-child').forEach(item => {
                    item.textContent = '+';
                });

                // Open current FAQ item
                answer.classList.add('active');
                icon.textContent = '−';
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = '#fff';
                header.style.backdropFilter = 'none';
            }
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe pricing cards
        document.querySelectorAll('.pricing-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Observe FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            observer.observe(item);
        });
    </script>
</body>
</html>
