import Cookies from 'js-cookie'

/**
 * 统一的API配置工具函数
 * 用于获取API基础地址和相关配置
 */
export interface ApiConfig {
  apiBase: string
  user: string
  yudaoToken: string
}

/**
 * 获取API配置
 * 根据不同环境动态设置API基础地址
 */
export const getApiConfig = (userInfo?: any): ApiConfig => {
  // 获取域名
  const hostname = window.location.hostname
  let apiBase = 'http://localhost:3000'

  // 根据域名动态设置 API 基础地址
  if (hostname === 'ai.medon.com.cn') {
    apiBase = 'https://ai.medon.com.cn/dev-api'
  } else if (hostname === 'ai.medsci.cn') {
    apiBase = 'https://ai.medsci.cn/dev-api'
  }

  // 获取用户信息
  let user = 'nologin'
  if (userInfo?.userName) {
    user = userInfo.userName
  } else {
    // 从 localStorage 获取用户信息作为备选
    try {
      const storedUserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      user = storedUserInfo.userName || 'nologin'
    } catch (error) {
      console.warn('Failed to parse userInfo from localStorage:', error)
    }
  }

  return {
    apiBase,
    user,
    yudaoToken: Cookies.get('yudaoToken') || ''
  }
}
