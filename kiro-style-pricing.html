<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="解锁 Kiro 的全部潜力，在预览期间享受合理限制下的免费使用。">
    <meta name="keywords" content="<PERSON><PERSON>,定价,AI开发工具,代码助手">
    <meta name="author" content="Kiro">
    <meta property="og:title" content="定价 - Kiro">
    <meta property="og:description" content="解锁 Kiro 的全部潜力，在预览期间享受合理限制下的免费使用。">
    <meta property="og:type" content="website">
    <title>定价 - Kiro</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.5;
            color: #1a1a1a;
            background-color: #ffffff;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header - 完全复制 Kiro 样式 */
        header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            height: 72px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            text-decoration: none;
        }

        .logo-text {
            margin-right: 8px;
        }

        .preview-badge {
            background: #ff6b35;
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            font-size: 15px;
            transition: color 0.2s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: #1a1a1a;
        }

        .nav-links a.active {
            color: #1a1a1a;
        }

        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 8px;
        }

        .mobile-menu span {
            width: 20px;
            height: 2px;
            background: #1a1a1a;
            margin: 2px 0;
            transition: 0.3s;
            border-radius: 1px;
        }

        /* Hero Section - 完全复制 Kiro 样式 */
        .hero {
            text-align: left;
            padding: 60px 0 40px;
            background: #ffffff;
        }

        .hero h1 {
            font-size: 40px;
            font-weight: 400;
            line-height: 1.2;
            color: #1a1a1a;
            margin-bottom: 16px;
            letter-spacing: -0.01em;
        }

        .hero p {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 0;
        }

        /* Pricing Section - 完全复制 Kiro 样式 */
        .pricing {
            padding: 40px 0 60px;
            background: #ffffff;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 40px;
        }

        .pricing-card {
            background: #ffffff;
            border: none;
            border-radius: 0;
            padding: 0;
            position: relative;
            transition: none;
        }

        .pricing-card:hover {
            border-color: transparent;
            box-shadow: none;
            transform: none;
        }

        .plan-header {
            margin-bottom: 24px;
        }

        .plan-name {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
            text-transform: none;
            letter-spacing: normal;
        }

        .plan-badge {
            background: #fbbf24;
            color: #92400e;
            font-size: 11px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
            display: inline-block;
        }

        .plan-price {
            display: block;
            margin-bottom: 4px;
        }

        .plan-price .amount {
            font-size: 32px;
            font-weight: 400;
            color: #1a1a1a;
            line-height: 1;
        }

        .plan-period {
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 0;
        }

        .plan-features li {
            padding: 4px 0;
            color: #1a1a1a;
            font-size: 14px;
            line-height: 1.4;
            position: relative;
            padding-left: 16px;
        }

        .plan-features li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #1a1a1a;
            font-weight: normal;
        }

        /* FAQ Section - 完全复制 Kiro 样式 */
        .faq {
            padding: 80px 0;
            background: #ffffff;
        }

        .faq h2 {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 48px;
            letter-spacing: -0.02em;
        }

        .faq-item {
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 0;
        }

        .faq-question {
            padding: 24px 0;
            font-weight: 600;
            font-size: 16px;
            color: #1a1a1a;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: color 0.2s ease;
        }

        .faq-question:hover {
            color: #666;
        }

        .faq-answer {
            padding-bottom: 24px;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            display: none;
        }

        .faq-answer.active {
            display: block;
        }

        .faq-icon {
            font-size: 18px;
            transition: transform 0.2s ease;
        }

        .faq-item.active .faq-icon {
            transform: rotate(45deg);
        }

        /* Footer - 简化版 */
        footer {
            background: #f8f9fa;
            padding: 48px 0 24px;
            border-top: 1px solid #e5e5e5;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 48px;
            margin-bottom: 32px;
        }

        .footer-section h3 {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s ease;
        }

        .footer-section ul li a:hover {
            color: #1a1a1a;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 24px;
            border-top: 1px solid #e5e5e5;
            color: #666;
            font-size: 14px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 24px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu {
                display: flex;
            }

            .hero h1 {
                font-size: 36px;
            }

            .hero p {
                font-size: 18px;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .footer-content {
                grid-template-columns: repeat(2, 1fr);
                gap: 32px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 80px 0 60px;
            }

            .hero h1 {
                font-size: 28px;
            }

            .hero p {
                font-size: 16px;
            }

            .pricing {
                padding: 60px 0 80px;
            }

            .faq {
                padding: 60px 0;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <a href="/" class="logo">
                <span class="logo-text">Kiro</span>
                <span class="preview-badge">PREVIEW</span>
            </a>
            <ul class="nav-links" id="navLinks">
                <li><a href="/changelog/">更新日志</a></li>
                <li><a href="/pricing/" class="active">定价</a></li>
                <li><a href="/docs/">文档</a></li>
                <li><a href="/downloads/">下载</a></li>
            </ul>
            <div class="mobile-menu" id="mobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>解锁 Kiro 的全部潜力，免费使用</h1>
            <p>在我们的预览期间，享受合理限制下的免费使用。</p>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing">
        <div class="container">
            <div class="pricing-grid">
                <!-- Free Plan -->
                <div class="pricing-card">
                    <div class="plan-header">
                        <h3 class="plan-name">KIRO FREE</h3>
                        <span class="plan-badge">即将推出</span>
                        <div class="plan-price">
                            <span class="currency">$</span>
                            <span class="amount">0</span>
                        </div>
                        <div class="plan-period">/月/用户</div>
                    </div>
                    <ul class="plan-features">
                        <li>Kiro IDE 中的智能助手功能（每月限制 50 次交互）</li>
                        <li>规格配置</li>
                        <li>代理钩子</li>
                        <li>模型上下文协议 (MCP)</li>
                        <li>代理引导</li>
                    </ul>
                </div>

                <!-- Pro Plan -->
                <div class="pricing-card">
                    <div class="plan-header">
                        <h3 class="plan-name">KIRO PRO</h3>
                        <span class="plan-badge">即将推出</span>
                        <div class="plan-price">
                            <span class="currency">$</span>
                            <span class="amount">19</span>
                        </div>
                        <div class="plan-period">/月/用户</div>
                    </div>
                    <ul class="plan-features">
                        <li>包含 Kiro Free 的所有功能，以及：</li>
                        <li>增强的 Kiro 智能助手功能限制（总限制：每月 1,000 次交互）</li>
                    </ul>
                </div>

                <!-- Pro+ Plan -->
                <div class="pricing-card">
                    <div class="plan-header">
                        <h3 class="plan-name">KIRO PRO+</h3>
                        <span class="plan-badge">即将推出</span>
                        <div class="plan-price">
                            <span class="currency">$</span>
                            <span class="amount">39</span>
                        </div>
                        <div class="plan-period">/月/用户</div>
                    </div>
                    <ul class="plan-features">
                        <li>包含 Kiro Pro 的所有功能，以及：</li>
                        <li>增强的 Kiro 智能助手功能限制（总限制：每月 3,000 次交互）</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2>常见定价问题</h2>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>我能免费使用 Kiro 多长时间？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    在预览期间，您可以无限期免费使用 Kiro 的基础功能。我们会在正式发布前至少提前 30 天通知任何变更。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>预览期间是否有 Kiro 限制？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    是的，为了确保服务质量和公平使用，我们在预览期间设置了合理的使用限制。免费版每月限制 50 次智能助手交互。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>预览期结束后会发生什么？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    预览期结束后，我们将推出正式的定价计划。现有用户将享受特殊的过渡优惠，我们会提前通知所有变更。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>我是 Amazon Q Developer Pro 用户。我可以使用 Kiro 吗？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    是的，Kiro 与 Amazon Q Developer Pro 兼容。您可以在现有的开发环境中无缝集成 Kiro 的功能。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>Kiro 中的智能助手交互是如何定义的？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    一次智能助手交互是指您向 Kiro AI 助手发送一个请求并收到回复的完整过程。复杂的多轮对话可能计算为多次交互。
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>我可以通过 Kiro Pro 或 Pro+ 订阅购买额外的代理交互吗？</span>
                    <span class="faq-icon">+</span>
                </div>
                <div class="faq-answer">
                    目前我们不提供单独购买额外交互的选项。如果您需要更多交互次数，建议升级到更高级别的订阅计划。
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>产品</h3>
                    <ul>
                        <li><a href="/about/">关于 Kiro</a></li>
                        <li><a href="/pricing/">定价</a></li>
                        <li><a href="/changelog/">更新日志</a></li>
                        <li><a href="/downloads/">下载</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>资源</h3>
                    <ul>
                        <li><a href="/docs/">文档</a></li>
                        <li><a href="/blog/">博客</a></li>
                        <li><a href="/faq/">常见问题</a></li>
                        <li><a href="/feedback/">提交反馈</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>社交</h3>
                    <ul>
                        <li><a href="#">Discord</a></li>
                        <li><a href="#">LinkedIn</a></li>
                        <li><a href="#">Twitter</a></li>
                        <li><a href="#">Instagram</a></li>
                        <li><a href="#">YouTube</a></li>
                        <li><a href="#">Bluesky</a></li>
                        <li><a href="#">Twitch</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>法律</h3>
                    <ul>
                        <li><a href="#">网站条款</a></li>
                        <li><a href="#">许可证</a></li>
                        <li><a href="#">负责任的 AI 政策</a></li>
                        <li><a href="#">法律</a></li>
                        <li><a href="#">隐私政策</a></li>
                        <li><a href="#">Cookie 偏好</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Kiro. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenu = document.getElementById('mobileMenu');
        const navLinks = document.getElementById('navLinks');

        mobileMenu.addEventListener('click', () => {
            navLinks.classList.toggle('active');

            // Animate hamburger menu
            const spans = mobileMenu.querySelectorAll('span');
            if (navLinks.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = 'none';
                spans[1].style.opacity = '1';
                spans[2].style.transform = 'none';
            }
        });

        // FAQ toggle function - 完全复制 Kiro 的行为
        function toggleFaq(element) {
            const faqItem = element.parentElement;
            const answer = element.nextElementSibling;
            const icon = element.querySelector('.faq-icon');

            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (item !== faqItem) {
                    item.classList.remove('active');
                    item.querySelector('.faq-answer').classList.remove('active');
                    item.querySelector('.faq-icon').textContent = '+';
                }
            });

            // Toggle current FAQ item
            if (faqItem.classList.contains('active')) {
                faqItem.classList.remove('active');
                answer.classList.remove('active');
                icon.textContent = '+';
            } else {
                faqItem.classList.add('active');
                answer.classList.add('active');
                icon.textContent = '+';
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect - 完全复制 Kiro 的效果
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            const currentScrollY = window.scrollY;

            if (currentScrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.9)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.8)';
                header.style.borderBottomColor = 'rgba(0, 0, 0, 0.05)';
            }

            lastScrollY = currentScrollY;
        });

        // Intersection Observer for animations - 复制 Kiro 的动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe pricing cards with staggered animation
        document.querySelectorAll('.pricing-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
            observer.observe(card);
        });

        // Observe FAQ items
        document.querySelectorAll('.faq-item').forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(10px)';
            item.style.transition = `opacity 0.4s ease ${index * 0.05}s, transform 0.4s ease ${index * 0.05}s`;
            observer.observe(item);
        });
    </script>
</body>
</html>
