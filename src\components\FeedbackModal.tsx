import React, { useState, useEffect, useRef } from 'react'
import { Modal, Form, Input, Button, message } from 'antd'
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks'
import { useLoginStatus } from '../hooks/useLoginStatus'
import { FeedbackData } from '../api/src/xai-api'
import { getApiConfig } from '../utils/apiConfig'

const { TextArea } = Input

interface FeedbackModalProps {
  open: boolean
  onCancel: () => void
}

// 错误信息接口
interface ConsoleError {
  message: string
  source?: string
  line?: number
  column?: number
  timestamp: number
  type: 'error' | 'unhandledrejection'
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({ open, onCancel }) => {
  const { t, currentLanguage } = useSimpleTranslation()
  const { currentAppName, currentSessionId } = useI18nRouter()
  const { userInfo } = useLoginStatus()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const consoleErrors = useRef<ConsoleError[]>([])
  const errorListenerAdded = useRef(false)

  // 获取默认邮箱
  const getDefaultEmail = () => {
    const hostname = window.location.hostname
    if (hostname === 'ai.medon.com.cn') {
      return '<EMAIL>'
    } else if (hostname === 'ai.medsci.cn') {
      return '<EMAIL>'
    }
    return '<EMAIL>'
  }

  // 收集控制台错误
  useEffect(() => {
    if (errorListenerAdded.current) return

    const handleError = (event: ErrorEvent) => {
      consoleErrors.current.push({
        message: event.message,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        timestamp: Date.now(),
        type: 'error'
      })
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      consoleErrors.current.push({
        message: String(event.reason),
        timestamp: Date.now(),
        type: 'unhandledrejection'
      })
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    errorListenerAdded.current = true

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])



  // 格式化反馈内容
  const formatFeedbackContent = (userContent: string) => {
    // 收集系统信息（移除操作环境字段）
    const systemInfo = [
      `浏览器信息: ${navigator.appName} ${navigator.appVersion}, ${navigator.platform}`,
    ]

    // 如果在对话界面，添加会话ID
    if (currentSessionId) {
      systemInfo.push(`会话ID: ${currentSessionId}`)
    }

    // 添加控制台错误信息
    if (consoleErrors.current.length > 0) {
      const errorMessages = consoleErrors.current
        .map(err => `${err.type}: ${err.message}${err.source ? ` at ${err.source}:${err.line}:${err.column}` : ''}`)
        .join('\n')
      systemInfo.push(`console err: ${errorMessages}`)
    } else {
      systemInfo.push('console err: 无错误')
    }

    // 组合用户内容和系统信息
    return `用户输入内容：${userContent}\n\n${systemInfo.join('\n')}`
  }

  // 提交反馈
  const handleSubmit = async (values: { mgTitle: string; mgContent: string }) => {
    if (!userInfo) {
      message.error(t('feedback.loginRequired'))
      return
    }

    setLoading(true)

    try {
      // 格式化反馈内容
      const formattedContent = formatFeedbackContent(values.mgContent)

      // 构建应用名称+语言后缀+括号（意见反馈）作为标题
      const feedbackText = currentLanguage === 'zh' ? '意见反馈' : 'Feedback'
      const titleWithAppAndLang = `${currentAppName}-${currentLanguage}（${feedbackText}）`

      const feedbackData: FeedbackData = {
        projectId: 1,
        mgRealName: userInfo.realName || userInfo.userName || '', // 保持原有的用户信息
        mgTell: userInfo.mobile || '',
        mgEmail: userInfo.email || getDefaultEmail(), // 使用默认邮箱
        mgUnit: '',
        mgIsPublic: '1',
        mgTitle: titleWithAppAndLang, // 使用应用名称+语言后缀作为标题
        mgContent: formattedContent // 使用格式化后的内容
      }

      // 直接使用fetch调用，绕过baseRequest的JSON解析问题
      const apiConfig = getApiConfig(userInfo)
      const response = await fetch(`${apiConfig.apiBase}/ai-base/openapi/feedback?locale=${currentLanguage}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(apiConfig.yudaoToken ? { Authorization: `Bearer ${apiConfig.yudaoToken}` } : {}),
        },
        body: JSON.stringify(feedbackData),
      })

      console.log('API响应状态:', response.status) // 调试日志

      if (response.ok) {
        const responseData = await response.json()
        console.log('API响应数据:', responseData) // 调试日志

        // 正确解析API响应格式：{"status":200,"message":"成功","data":null}
        if (responseData && responseData.status === 200) {
          message.success(t('feedback.submitSuccess'))
          form.resetFields()
          onCancel()
        } else {
          throw new Error(responseData?.message || 'Submit failed')
        }
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('提交反馈失败:', error)
      message.error(t('feedback.submitError'))
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={t('navigation.feedback')}
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      centered
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="mt-4"
      >
        {/* 隐藏标题输入框，标题将自动生成为应用名称+语言后缀 */}
        <Form.Item
          name="mgTitle"
          style={{ display: 'none' }}
          initialValue="feedback" // 设置一个默认值以通过表单验证
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="mgContent"
          label={t('feedback.contentLabel')}
          rules={[
            { required: true, message: t('feedback.contentRequired') },
            { min: 10, message: t('feedback.contentMinLength') },
            { max: 1000, message: t('feedback.contentMaxLength') }
          ]}
        >
          <TextArea
            placeholder={t('feedback.contentPlaceholder')}
            rows={6}
            maxLength={1000}
            showCount
          />
        </Form.Item>

        <div className="flex justify-end gap-3 mt-6">
          <Button onClick={handleCancel}>
            {t('common.cancel')}
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {t('feedback.submit')}
          </Button>
        </div>
      </Form>
    </Modal>
  )
}

export default FeedbackModal
